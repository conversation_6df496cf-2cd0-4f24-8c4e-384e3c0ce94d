-- common.lua
-- 该文件包含游戏中通用的别名和触发器，处理例如登录、重连、死亡、发呆检测等通用事件。

-- @alias wdj
-- @param params 别名参数 
add_alias("wdj", function(params)
    var["do_stop"] = 0
    var["fail_xuezhu"] = "try"
    exec("do_quest wudujiao")
end)

--- 初始化地图标记。
-- 根据 var 表中的设置，初始化 standard_flags 和 lua_flags。
-- 此函数提取了原 import_setting 别名中的重复逻辑。
local function initialize_map_flags()
    standard_flags = {
        ["wudujiao"] = var["close_wudujiao"] or 1, ["yideng"] = var["close_yideng"] or 1,
        ["wudanghoushan"] = var["close_wudang"] or 1, ["xiaofu"] = var["mapxiaofu"] or 1,
        ["hudiegu"] = 1, ["night"] = 1, ["taohuadao"] = 1, ["shenlongdao"] = 1,
        ["murong"] = 1, ["shop"] = 1, ["male"] = 1, ["female"] = 1,
        ["party_thd"] = 1, ["party_gumu"] = 1, ["party_yangguo"] = 1, ["party_kunlun"] = 1,
        ["master_yideng"] = 1, ["fomuquan"] = 1, ["quest_wuliang"] = 1,
        ["mix_yideng"] = var["close_yideng"] or 1, ["mix_yideng_male"] = 1, ["mix_yideng_female"] = 1,
        ["mix_thd"] = 1, ["mix_thd_male"] = 1, ["mix_thd_female"] = 1,
        ["master_notzhangsanfeng"] = 1, ["master_zhangsanfeng"] = 0,
    }
    lua_flags = {}
    for k, v in pairs(standard_flags) do lua_flags[k] = v end
end

--- 从指定文件加载账号ID和密码到全局变量 `var` 中。
-- @param file_path 账号信息文件的路径。
local function load_account_from_file(file_path)
    local file = io.open(file_path, "r")
    if not file then echo(C.R .. "错误: 无法打开账号信息文件 " .. file_path); return end
    for line in file:lines() do
        if string.find(line, "【账号id】") then
            var["id"] = string.match(line, ".*=(.*)"); var["char_id"] = var["id"]; echo("\n" .. C.y .. "账号ID: " .. var["id"])
        elseif string.find(line, "【账号密码passwd") then
            var["passwd"] = string.match(line, ".*=(.*)"); echo(C.y .. "密码: " .. var["passwd"])
        end
    end
    file:close()
end

--- 从指定文件加载自定义配置（var变量）到全局变量 `var` 中。
-- @param file_path 配置文件路径。
local function load_config_from_file(file_path)
    echo("\n" .. C.x .. "【导入设置】")
    local file = io.open(file_path, "r")
    if not file then echo(C.R .. "错误: 无法打开配置文件 " .. file_path); return end
    for line in file:lines() do
        local m1, m2 = string.match(line, "^var%[(.*)%]%=(.*)")
        if m1 then
            m1 = string.gsub(m1, '%"', ""); m2 = string.gsub(m2, '%"', "")
            local m3 = tonumber(m2)
            if m3 == nil then
                var[m1] = m2; echo(C.y .. 'var["' .. m1 .. '"]="' .. m2 .. '"')
            else
                var[m1] = m3; echo(C.y .. 'var["' .. m1 .. '"]=' .. m3 .. '')
            end
        end
    end
    file:close()
end

--- 初始化任务相关的全局变量。
local function initialize_job_variables()
    var["do_stop"] = 0; var["wrong_way"] = 0
    var["step_wait"] = var["walk_wait"] / var["walk_step"]
    var["do_job"] = ""; add_alias("do_job", function() end)
    var["xiaofu"] = 0
end

--- 封装通用定时器清除逻辑。
-- 用于在登录、死亡、退出等场景下清除所有或大部分活跃的定时器。
local function clear_common_timers()
    del_timer("timer"); del_timer("timer2"); del_timer("checkidle")
    del_timer("check_busy_1"); del_timer("check_busy_2"); del_timer("zhanglao")
    del_timer("wait"); del_timer("wait1")
end

--- 导入用户设置的别名。
-- 根据环境（是否存在 GetVar 函数）从文件或游戏内变量加载ID、密码和配置。
-- 加载完成后，会初始化地图标记和任务相关变量。
-- @alias import_setting
-- @param params 别名参数 。
add_alias("import_setting", function(params)
    if type(GetVar) == "nil" then
        echo(C.x .. "【变量导入】")
        load_account_from_file(".\\账号信息.txt")
        load_config_from_file(".\\my-config.txt")
        initialize_job_variables()
        initialize_map_flags()
    else
        echo(C.R .. "【变量导入】")
        local id = GetVar('id') or ""; local passwd = GetVar('passwd') or ""
        if id == "" or passwd == "" then
            for i = 1, 5 do echo(C.R .. "警告" .. C.Y .. ":" .. C.W .. "未检测到id和密码！！！") end
        else
            var['id'] = id; var['passwd'] = passwd; var["char_id"] = id
            loadsetting()
            initialize_map_flags()
        end
    end
end)

--- 捕获游戏登录成功信息，自动登录角色。
-- 在检测到登录界面特征时触发，清除旧定时器并执行登录流程。
-- @trigger common_1
-- @pattern ^\s*Best view with
-- @param params 触发器参数 。
add_trigger("common_1", "^[ > ]*Best view with", function(params)
    local id = GetVar('id') or ""; local passwd = GetVar('passwd') or ""
    if id == "" or passwd == "" then
        for i = 1, 5 do echo(C.R .. "警告" .. C.Y .. ":" .. C.W .. "未检测到id和密码！！！") end
    else
        clear_common_timers()
        exec("login_id")
        local quit = var["quit"] or 0
        if quit == 0 then
            exec("import_setting"); require "fight"; close_fight()
            var["quit"] = nil; add_alias("login_id", function(params) end)
            send("n"); send(var["id"]); send(var["passwd"]); send("y")
        end
    end
end)

--- 定时检查玩家是否发呆（无响应）。
-- 每60秒检查一次，根据发呆时间长短，执行断线或重启任务逻辑。
function check_idle()
    set_timer2("checkidle_mods", 60, function()
        local idle = var["idle"] or 0; local quit = var["quit"] or 0
        local os_date = os.date("%m/%d %H:%M:%S"); local receive_byte = get_receive()
        Echo(C.x .. "总字节数:" .. C.x .. receive_byte .. C.x .. " KB.")

        if idle >= 19 then -- 20分钟发呆
            if var["receive_byte"] and var["receive_byte"] == receive_byte then
                local id = var["char_id"] or "none"
                echo(C.W .. "<解密>:" .. os_date .. C.x .. "【玩家:" .. id .. "】系统无反馈，关闭外挂。")
                process_client_line("`quit")
            else
                if quit ~= 1 then
                    var["quit"] = 0; var["idle"] = 0; var["do_stop"] = 0
                    local id = var["char_id"] or "none"
                    echo("\n" .. C.r .. "<注意>:" .. os_date .. C.x .. "【玩家:" .. id .. "】未知原因导致发呆20分钟，重启任务。")
                    log_to_txt("fadai", var.id); exec("changejob")
                end
            end
        elseif idle >= 4 then -- 5分钟发呆
            if var["receive_byte"] and var["receive_byte"] == receive_byte then
                local id = var["char_id"] or "none"
                echo("\n" .. C.c .. "<解密>:" .. os_date .. C.c .. "【玩家:" .. id .. "】系统无反馈，关闭外挂。")
                process_client_line("`quit")
            else
                if quit ~= 1 then
                    var["idle"] = 0; var["do_stop"] = 0
                    log_to_txt("fadai", var.id); exec("changejob")
                end
            end
        end

        if math.mod(idle, 5) == 0 then var["receive_byte"] = receive_byte end
        idle = idle + 1; var["idle"] = idle
        echo("\n" .. C.x .. "<发呆>:" .. C.r .. var["idle"] .. C.x .. "次,共5次。")
    end)
end
check_idle() -- 启动发呆检测定时器

--- 处理创建新角色时名字不合法的提示。
-- 如果设置了 new_char 变量，则自动执行武馆开局流程。
-- @trigger common_2
-- @pattern ^\s*对不起，您的英文名字必须是.*个英文字母。
-- @param params 触发器参数 。
add_trigger("common_2", "^[ > ]*对不起，您的英文名字必须是.*个英文字母。", function(params)
    local new_char = var["new_char"] or ""
    if new_char ~= "" then
        alarm("wait", 8, function()
            require "wuguan"; open_wuguan()
            for k in string.gmatch(var["new_char"] .. ";", "(%S-);") do send(k) end
            exec("do_wg")
        end)
    end
end)

--- 捕获玩家死亡或昏迷的信息。
-- 重置任务状态并清除相关定时器。
-- @trigger common_3
-- @pattern ^\s*你只觉得头昏脑胀，眼前一黑，接着什么也不知道了|你「啪」的一声倒在地上，挣扎着抽动了几下就死了。
-- @param params 触发器参数 。
add_trigger("common_3", "^[ > ]*你只觉得头昏脑胀，眼前一黑，接着什么也不知道了|你「啪」的一声倒在地上，挣扎着抽动了几下就死了。", function(params)
    if var["quest_die"] and var["quest_die"] == 0 then var["quest_die"] = 1 end
    clear_common_timers(); var["do_stop"] = 0
end)

--- 捕获玩家意外死亡的信息，记录日志并根据设置决定后续操作。
-- 意外死亡通常指被游戏机制杀死，而非战斗。
-- @trigger common_4
-- @pattern ^\s*(?:一股阴冷的浓雾突然出现，很快地包围了你。|迷迷糊糊中，你觉得自己又堕入了滚滚轮回|觉得自己的意识越来越弱，渐渐不省人事了)
-- @param params 触发器参数 。
add_trigger("common_4", "^[ > ]*(?:一股阴冷的浓雾突然出现，很快地包围了你。|迷迷糊糊中，你觉得自己又堕入了滚滚轮回|觉得自己的意识越来越弱，渐渐不省人事了)", function(params)
    local id = var["char_id"] or "none"; local os_date = os.date("%m/%d %H:%M")
    echo("\n" .. C.c .. "<警告>:" .. os_date .. C.x .. "【" .. id .. "】意外死亡！！！")
    log_to_txt("die", var.id)
    local job_after_die = var["job_after_die"] or 1
    if job_after_die == 1 then
        check_busy(function() exec(var["skills_bei_nk"]); send("alias pfm " .. var["pfm4"]); send("set wimpycmd hp\\pfm"); exec("start") end)
    else
        if var["job"] and var["job"] == "tdh" then exec("do_quit die")
        else check_busy(function() exec("start") end) end
    end
end)

--- 捕获从桃花阵中被背出的信息。
-- 触发后设置 `after_faint` 别名，在恢复后执行GPS定位和 `after_faint2`。
-- @trigger common_5
-- @pattern ^\s*迷迷胡胡中似乎有人把你背出了阵。
-- @param params 触发器参数 。
add_trigger("common_5", "^[ > ]*迷迷胡胡中似乎有人把你背出了阵。", function(params)
    add_alias("after_faint", function(params) exec("after_faint2"); exec("gps") end)
end)

--- 捕获重连游戏的信息，执行初始化操作。
-- 在重新连接成功后，重置迷宫NPC相关函数，并重新启动初始化流程。
-- @trigger common_6
-- @pattern ^\s*(?:人物目前正在游戏当中，您可以取而代之，确定请|您上次连线地址是|重新连线完毕。)
-- @param params 触发器参数 。
add_trigger("common_6", "^[ > ]*(?:人物目前正在游戏当中，您可以取而代之，确定请|您上次连线地址是|重新连线完毕。)", function(params)
    npc_in_maze_found = function() return nil end
    npc_in_maze_action = function() return nil end
    exec("start")
    local id = var["char_id"] or "none"; local os_date = os.date("%m/%d %H:%M:%S")
    echo("\n" .. C.W .. "<连线>:" .. os_date .. C.W .. "【" .. id .. "】连线进入游戏！")
end)

--- 游戏开始或重连后的初始化操作。
-- 重置各种状态变量，清除定时器，并执行一系列恢复和准备动作，包括装备、药品和技能设置。
-- @alias start
-- @param params 别名参数 。
add_alias("start", function(params)
    clear_common_timers()
    var["quit"] = nil; add_alias("login_id", function(params) end)
    var["do_stop"] = 0; var["idle"] = 0; var["fight"] = 0; var["crush"] = 0; var["crush_id"] = ""
    if (var["wudi"] or 0) > 1 then var["wudi"] = 1 end
    var["time_mark_liaobed"] = os.time()

    local job_fail_wait = var["job_fail_wait"] or 1
    if job_fail_wait > 0 then
        if string.find(var["job_list"] or "", "wd") then
            alarm("job_fail_wait", 540, function() var["job_fail_wait"] = nil end)
        else var["job_fail_wait"] = nil end
    end

    var["mapdalihuanggong"] = 0; var["mapxiaofu"] = 0; var["mapwudujiao"] = 0
    var["maptiezhang"] = 0; var["shenlongjiaodalishu"] = 0; var["mapjueqinggu"] = nil

    wait1(14, function()
        send("score"); send("drop shoe"); send("drop shoes"); send("get boot")
        exec("get @myweapon"); send("get @weapon"); send("get jinyuan san"); send("get fire")
        send("get chuanbei wan"); send("get tianqi")
        send("set no_beg"); send("set wimpy 100")
        exec(var["skills_bei5"]); send("alias pfm " .. var["pfm5"]); send("set wimpycmd pfm\\hp")
        g(58, function()
            unset_timer("timer")
            wait1(10, function()
                exec("get tianqi from zhan;se;e;s;s;s;s;e;s;qu tianqi;dlist;changejob")
            end)
        end)
    end)
end)

--- 捕获从昏迷中恢复知觉的信息。
-- 根据当前任务标志决定是检测挑战状态还是重新装备并检查中毒。
-- @trigger common_7
-- @pattern ^\s*一股暖流发自丹田流向全身，慢慢地你又恢复了知觉
-- @param params 触发器参数 。
add_trigger("common_7", "^[ > ]*一股暖流发自丹田流向全身，慢慢地你又恢复了知觉", function(params)
    if var["flag_job"] == "quest" then exec("alias action 挑战状态检测")
    else exec("get @myweapon;wear all;check_poison after_faint") end
end)

--- 捕获被攻击的信息，如果攻击者在危险列表里，则执行逃跑。
-- @trigger common_8
-- @pattern ^\s*看起来(\S+)想杀死你！
-- @param params 触发器参数，params[1]为攻击者名字。
add_trigger("common_8", "^[ > ]*看起来(\\S+)想杀死你！", function(params)
    local d = var["danger_list"] or {}; local name = params[1]
    for k, v in pairs(d) do if string.find(v, name) then exec("job_escape"); break end end
end)

--- 捕获被 NPC 嘲讽的信息，如果对方在危险列表里，则执行逃跑。
-- @trigger common_9
-- @pattern ^\s*(\S+)说道：「Kao，还敢追来，再练一百年吧！！！！」
-- @param params 触发器参数，params[1]为嘲讽者名字。
add_trigger("common_9", "^[ > ]*(\\S+)说道：「Kao，还敢追来，再练一百年吧！！！！」", function(params)
    local d = var["danger_list"] or {}; local name = params[1]
    for k, v in pairs(d) do if string.find(v, name) then exec("job_escape"); break end end
end)

--- 捕获正在退出的信息，清理定时器。
-- @trigger common_10
-- @pattern ^\s*(?:正在退出游戏|过程中|正在退出过程中)
-- @param params 触发器参数 。
add_trigger("common_10", "^[ > ]*(?:正在退出游戏|过程中|正在退出过程中)", function(params)
    clear_common_timers()
end)

--- 处理一系列杂项游戏提示信息。
-- 根据不同的消息内容，执行相应的逻辑，如更新情怀币数量、切换任务、处理毒针等。
-- @trigger common_11
-- @pattern 多个模式，通过 | 连接
-- @param params 触发器参数，params[0]为完整匹配的字符串。
add_trigger("common_11", "^[ > ]*(?:你只觉得力不从心，凭现在的轻功很难跃上远处的小舟。|好象是被喂过剧毒，不过效果已经不明显了。|一枚情怀币！|你没有这么多潜能。|虚通拦住你.*少林千年的规矩，外客不得持兵刃上山。|你已经感到不行了，冥冥中你觉得有人将你抬到吐谷浑伏俟城中。|由于你已经听了神雕侠杨过的指导，所以不用多想，信步就走出了林海。)", function(params)
    local msg = params[0]
    if string.find(msg, "枚情怀币！") then
        var["win_tongbao"] = msg; local n = tonumber(trans(string.match(msg, "(%S+)枚情怀币！")))
        if n then var["my_tongbao"] = (var["my_tongbao"] or 0) + n; quest_log("tongbao") end
    elseif string.find(msg, "你只觉得力不从心，凭现在的轻功很难跃上远处的小舟。") then exec("changejob")
    elseif string.find(msg, "你没有这么多潜能。") then unset_timer("timer"); exec("changejob")
    elseif string.find(msg, "好象是被喂过剧毒，不过效果已经不明显了。") and string.find(line[-1] or "", "一枚蓝莹莹的细针，通体晶莹，偶尔有一丝蓝亮游动，似乎喂有剧毒。") then var["drop_duzhen"] = "yes"
    elseif string.find(msg, "外客不得持兵刃上山。") then
        send("i"); local equip = var["equipment_s"] or {}
        for k, v in pairs(equip) do send("unwield " .. k); send("unwield " .. k .. " 2") end
    elseif string.find(msg, "你已经感到不行了，冥冥中你觉得有人将你抬到吐谷浑伏俟城中。") then
        clear_common_timers(); add_alias("after_faint", function(params) exec("gps") end)
    elseif string.find(msg, "由于你已经听了神雕侠杨过的指导，所以不用多想，信步就走出了林海。") then
        var["havextjf"] = true; if not var["mapxuantie"] and var["havextjf"] then add_map("删除玄铁剑迷宫") end
    end
end)

--- 记录任务奖励的经验和潜能，并根据设置随机发送聊天信息。
-- @trigger common_12
-- @pattern ^\s*(\S+)点潜能!$
-- @param params 触发器参数，params[1]为潜能点数。
add_trigger("common_12", "^[ > ]*(\\S+)点潜能!$", function(params)
    var["log_pot"] = params[1]; var["log_exp"] = string.match(line[2], "(%S+)点经验!$")
    do_log("job_reward")
    local say = get_random_say(); local random_say = var["random_say"] or 0
    if say ~= "" and math.random(150) == 26 and random_say == 1 then send("chat " .. say) end
end)

--- 当执行 action 别名时，如果不是 GPS 寻路，则删除 check 定时器。
-- @trigger common_13
-- @pattern ^\s*你把\s+"action"
-- @param params 触发器参数，params[-1]为前一行内容。
add_trigger("common_13", "^[ > ]*你把\\s+\"action\"", function(params)
    if not string.find(params[-1], "GPS routing") then del_timer("check") end
end)

--- 收到他人 tell 消息时自动回复当前状态。
-- 回复当前位置、经验和经验速率。
-- @trigger common_14
-- @pattern ^\s*(\S+)\((.+)\)告诉你：(.*)
-- @param params 触发器参数，params[1]为说话者名字，params[2]为ID，params[3]为消息内容。
add_trigger("common_14", "^[ > ]*(\\S+)\\((.+)\\)告诉你：(.*)", function(params)
    local tell_id = string.lower(params[2])
    if not string.find(tell_id, "xue muhua") then
        send("reply " .. "当前位于:" .. (var["roomzone"] or "") .. " " .. (var["roomname"] or "") .. ", exp:" .. (var["exp"] or "none") .. ", expspeed:" .. (var["expspeed"] or "none"))
    end
end)

--- 少林弟子被玄痛大师召见时自动回复。
-- @trigger common_15
-- @pattern ^\s*(\S+)，戒律院玄痛大师请你去陈述此次下山经过 ！
-- @param params 触发器参数，params[1]为被召见者名字。
add_trigger("common_15", "^[ > ]*(\\S+)，戒律院玄痛大师请你去陈述此次下山经过 ！", function(params)
    if not var["shaolintiaoshui"] and var["char_name"] and params[1] == var["char_name"] and var["party"] and var["party"] == "少林派" then
        var["shaolintiaoshui"] = 1; send("wisper seng bing 你烦不烦，火大...")
    end
end)

--- 卸下所有已装备的武器。
-- 遍历 `equipment_s` 变量，对每个装备的武器执行卸下命令。
-- @alias unwields
-- @param params 别名参数 。
add_alias("unwields", function(params)
    local equip = var["equipment_s"] or {}
    for k, v in pairs(equip) do send("unwield " .. k); send("unwield " .. k .. " 2") end
end)

--- 设置一个标记，在当前任务完成后重载脚本。
-- @alias reboot
-- @param params 别名参数 。
add_alias("reboot", function(params)
    var["reboot"] = 1; log_message("当前任务完成后将重载脚本！！！！", C.R, "<系统>")
end)

--- 立即重新加载所有脚本模块和配置。
-- 用于开发和调试，无需重启整个客户端即可应用代码更改。
-- 该功能会清除 Lua `package.loaded` 缓存并重新 `require` 模块。
-- @alias reload_all_modules
-- @param params 别名参数 。
add_alias("reload_all_modules", function(params)
    log_message("正在通过 require 重新加载所有脚本...", C.Y, "<系统>")
    local modules_to_reload = {
        "trigger", "alias", "function", "var", "place", "timer", "copytable", "cmud", "rooms", "dazuo", "walk", "gps",
        "dujiang", "boat", "maze", "guard", "trade", "common", "fight", "job", "story", "fullskills",
        "lingwu_lianxi", "log", "bigwords", "quest.emjy", "Instance.ttt", "Instance.challenge", "jueji",
        "Instance.qinghuaimeng", "Instance.haizhan", "prepare", "quit", "quest", "heal", "repair", "status", "import_settings","ui",
    }
    for _, module_name in ipairs(modules_to_reload) do
        package.loaded[module_name] = nil; require(module_name)
    end
    local job_modules_to_clear_cache = {
        "job.xueshan", "job.changlebang", "job.xuncheng", "job.guanfu", "job.wudang", "job.gaibang", "job.songshan",
        "job.huashan", "job.xunluo", "job.husong", "job.songxin", "job.songmoya", "job.xiangyangshouwei"
    }
    for _, module_name in ipairs(job_modules_to_clear_cache) do
        package.loaded[module_name] = nil
    end
    log_message("脚本重载完成，正在加载配置...", C.Y, "<系统>"); exec("import_setting")
    log_message("所有模块和配置已成功重载！！！！", C.R, "<解密>")
end)

Print("--- 加载模块: 一般触发 ---")