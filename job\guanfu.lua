-- ============================================================================
-- 官府任务模块 (guanfu.lua)
-- 功能：处理朝廷公差任务的完整流程
-- 作者：重构版本
-- 版本：2.0
-- ============================================================================

-- ============================================================================
-- 常量定义
-- ============================================================================

-- 默认配置常量
local GUANFU_CONFIG = {
	-- 放弃搜索的区域列表
	DEFAULT_FANGQI_ZONE = "神龙岛|天山|曼陀罗山庄|燕子坞|姑苏慕容|黑木崖",
	-- 放弃搜索的房间列表
	DEFAULT_FANGQI_ROOM = "九老洞|后山小院",
	-- 默认搜索次数
	DEFAULT_SEARCH_ATTEMPTS = 2,
	-- 搜索范围
	SEARCH_RANGE = 5,
	-- 经验倍数
	DEFAULT_EXP_MULTIPLE = 2,
	-- 默认经验值
	DEFAULT_EXP = 150000,
	-- 默认等级
	DEFAULT_LEVEL = 51,
	-- 默认等级名称
	DEFAULT_LEVEL_NAME = "返璞归真",
	-- 官府房间号
	GUANFU_ROOM = 121,
	-- 存文书房间号
	WENSHU_ROOM = 80,
	-- 交差房间号
	JIAOCHAI_ROOM = 149
}

-- 地点修正映射表
local LOCATION_FIX_MAP = {
	["山径"] = "天山山径",
	["福州城中心"] = "福州城城中心",
	["青石大道"] = "天山青石大道",
	["归云庄九宫桃花阵"] = "归云庄归云庄前",
	["兰州沙漠"] = "蒙古沙漠"
}

-- 地点前缀修正映射表
local LOCATION_PREFIX_MAP = {
	["少林寺"] = "嵩山少林"
}

-- ============================================================================
-- 初始化和重置
-- ============================================================================

-- 官府任务重置触发器
add_trigger("guanfu_0", "^[ > ]*【扬州知府通告】近日有恶贼行凶作乱，请各位江湖豪杰速来官府领取铁捕文书，协助将彼等缉拿归案。", function(params)
	var["guanfu_reset"] = nil
	log_message("收到官府任务重置通知", C.y, "【朝廷公差】")
end)

-- ============================================================================
-- 工具函数
-- ============================================================================

-- 修正地点名称
local function fix_location_name(location)
	if not location then
		return nil
	end

	-- 精确匹配修正
	local corrected = LOCATION_FIX_MAP[location]
	if corrected then
		return corrected
	end

	-- 前缀匹配修正
	for prefix, replacement in pairs(LOCATION_PREFIX_MAP) do
		if string.sub(location, 1, string.len(prefix)) == prefix then
			return string.gsub(location, "^" .. prefix, replacement)
		end
	end

	return location
end

-- 获取配置值
local function get_config_value(key, default_value)
	return var[key] or default_value
end

-- 清理接取文书的触发器（不包括解析文书内容的触发器）
local function cleanup_jie_wenshu_triggers()
	delete_triggers("guanfu", 3, 5)
end

-- 清理解析文书内容的触发器
local function cleanup_look_wenshu_triggers()
	delete_triggers("guanfu", 6, 7)
end

-- 清理所有文书相关触发器
local function cleanup_all_wenshu_triggers()
	cleanup_jie_wenshu_triggers()
	cleanup_look_wenshu_triggers()
end

-- ============================================================================
-- 交差时的临时触发器管理
-- ============================================================================
-- 关闭交差时的错误处理触发器
local function disable_jiaochai_error_triggers()
	del_trigger("guanfu_temp_1")
end

-- 激活交差时的错误处理触发器
local function enable_jiaochai_error_triggers()
	add_trigger("guanfu_temp_1", "^[ > ]*你身上没有这样东西。", function(params)
		disable_jiaochai_error_triggers()
		log_message("【朝廷公差】:尸体腐烂消失，交不了任务，放弃！", C.y, "<Lua>", 0)
		gf_end_job()
	end)
end

-- 安全的交差函数
local function safe_give_to_zhao(item_name)
	enable_jiaochai_error_triggers()

	-- 3秒后自动清理临时触发器（成功的情况由guanfu_20处理，这里只处理失败）
	set_timer("cleanup_temp_triggers", 3, function()
		disable_jiaochai_error_triggers()
	end)

	exec("give " .. item_name .. " to zhao chengzhi")
end

-- 清理任务状态
local function reset_guanfu_state()
	var["job_npc_name"] = nil
	var["job_room"] = nil
	var["job_npc_id"] = nil
	var["killer_party"] = nil
	var["gf_target_name"] = "未知npc"
	var["no_need_heal_qi"] = nil
end

-- ============================================================================
-- 任务设置和别名定义
-- ============================================================================

add_alias("set_job_gf", function(params)
	close_guanfu()
	reset_guanfu_state()

	var["flag_job"] = "gf"
	set_wield_weapon("gf")
	send("alias bei_skills " .. var["skills_bei2"])

	-- 任务逃跑处理
	add_alias("job_escape", function(params)
		local killer_name = var["killer_name"] or "none"
		exec("set_danger_list " .. killer_name)
		set_black_list(killer_name)
		var["run"] = get_random_move(var["roomexit"])
		send("look")
		send("set wimpy 100")
		exec("set wimpycmd halt\\@run\\yun qi\\lead @pfm_id\\alias action 战斗失败，立即撤退...")
		exec("halt;get @myweapon;@run;yun qi;lead @pfm_id;alias action 战斗失败，立即撤退...")
		log_message("战斗失败，执行逃跑", C.r, "【朝廷公差】")
	end)

	-- 任务询问处理
	add_alias("job_ask", function(params)
		function after_gps()
			var["port"] = GUANFU_CONFIG.GUANFU_ROOM
			function after_goto()
				open_trigger("guanfu_1")
				check_busy(function()
					send("look wanted list")
				end)
			end

			exec("goto @port")
		end

		exec("gps")
	end)

	-- 任务失败处理
	add_alias("job_fail", function(params)
		reset_guanfu_state()
		close_guanfu()
		set_black_list(var["killer_name"])		
		gf_end_job()
		log_message("任务失败", C.r, "【朝廷公差】")
	end)

	-- 任务成功处理
	add_alias("job_win", function(params)
		reset_guanfu_state()
		close_guanfu()

		-- 任务成功后先去存文书房间取文书
		g(GUANFU_CONFIG.WENSHU_ROOM, function()
			check_busy(function()
				send("qu tiebu wenshu")
				check_busy(function()
					g(GUANFU_CONFIG.JIAOCHAI_ROOM, function()
						b(function()
							if var["job_type"] and var["job_type"] == "缉拿归案" then
								safe_give_to_zhao(var["killer_id"])
							else
								safe_give_to_zhao("corpse")
							end
						end)
					end)
				end)
			end)
		end)
		log_message("任务完成，前往交差", C.g, "【朝廷公差】")
	end)

	-- 昏迷后处理
	add_alias("after_faint", function(params)
		exec("job_fail")
	end)

	add_alias("after_faint2", function(params)
		exec("job_fail")
	end)

	-- NPC胜利处理
	add_alias("npc_win", function(params)
		var["job_finish"] = 0
		check_busy2(function()
			exec("yun qi;yun jing;yun jingli;hp;set wimpycmd pfm\\hp;kill @pfm_id;alias action 官府恶贼还在不...")
		end)
	end)

	-- NPC失败处理
	add_alias("npc_lose", function(params)
		var["job_finish"] = 0
		check_busy2(function()
			exec("yun qi;yun jing;yun jingli;hp;set wimpycmd pfm\\hp;kill @pfm_id;alias action 官府恶贼还在不...")
		end)
	end)
end)

-- ============================================================================
-- 通缉列表处理
-- ============================================================================

-- 通缉列表主触发器
add_trigger("guanfu_1", "^[ > ]*(?:本府现通缉以下罪犯：|目前治安良好，官府无任何通缉文书贴出。|揭榜请用\\s+jie.*通缉犯.*的格式。)", function(params)
	var["killer_name"] = nil
	var["killer_id"] = nil

	if string.find(params[-1], "本府现通缉以下罪犯") then
		-- 初始化通缉列表
		var["guanfu_wanted"] = {}
		log_message("开始解析通缉列表", C.y, "【朝廷公差】")

		-- 解析通缉犯信息
		add_trigger("guanfu_2", "^\\s+(\\S+)\\((.*)\\)\\s+，经验:\\s+(\\S+)\\s+，", function(params)
			local npc_info = {
				name = params[1],
				id = string.lower(params[2]),
				level_name = GUANFU_CONFIG.DEFAULT_LEVEL_NAME,
				level = GUANFU_CONFIG.DEFAULT_LEVEL,
				exp = tonumber(params[3]) or 0
			}
			var["guanfu_wanted"][params[1]] = npc_info
			--log_message("发现通缉犯：" .. params[1] .. "，经验：" .. params[3], C.c)
		end)
	elseif string.find(params[-1], "目前治安良好，官府无任何通缉文书贴出。") then
		var["guanfu_wanted"] = nil
		close_trigger("guanfu_1")
		log_message("目前无通缉犯", C.y, "【朝廷公差】")
		exec("check_gf_job")
	elseif string.find(params[-1], "揭榜请用") then
		close_trigger("guanfu_1")
		del_trigger("guanfu_2")
		log_message("获得通缉列表", C.g, "【朝廷公差】")
		exec("drop wenshu;check_gf_job")
	end
end)

-- ============================================================================
-- 任务检查和选择
-- ============================================================================

-- 过滤黑名单中的通缉犯
local function filter_wanted_list(wanted_list)
	local filtered_list = {}
	for name, npc_info in pairs(wanted_list) do
		if not check_black_list(name) then
			filtered_list[name] = npc_info
		end
	end
	return filtered_list
end

-- 选择合适的任务目标
local function select_target(filtered_list)
	local best_target = {
		level = GUANFU_CONFIG.DEFAULT_LEVEL,
		name = "",
		id = "",
		level_name = "",
		exp = 0
	}

	local my_exp = get_config_value("exp", GUANFU_CONFIG.DEFAULT_EXP)
	local exp_multiple = get_config_value("gf_job_multiple", GUANFU_CONFIG.DEFAULT_EXP_MULTIPLE)
	local max_exp = my_exp * exp_multiple

	for name, npc_info in pairs(filtered_list) do
		if npc_info.level <= best_target.level and npc_info.exp < max_exp then
			best_target.level = npc_info.level
			best_target.level_name = npc_info.level_name
			best_target.name = npc_info.name
			best_target.id = npc_info.id
			best_target.exp = npc_info.exp
		end
	end

	return best_target
end

-- 处理无任务情况
local function handle_no_job()
	var["guanfu_reset"] = "wait_refresh"
	var["fangqi_job"] = "gf"
	var["gf_target_name"] = "未知npc"
	set_black_list(var["killer_name"])
	gf_end_job()
	log_message("无可用任务，等待刷新", C.r, "【朝廷公差】")
end

-- 检查官府任务
add_alias("check_gf_job", function(params)
	local wanted_list = var["guanfu_wanted"] or {}

	-- 检查是否有通缉犯
	if null(wanted_list) then
		handle_no_job()
		return
	end

	-- 过滤黑名单
	local filtered_list = filter_wanted_list(wanted_list)
	if null(filtered_list) then
		log_message("所有通缉犯都在黑名单中", C.r, "【朝廷公差】")
		handle_no_job()
		return
	end

	-- 选择目标
	local target = select_target(filtered_list)
	if target.name == "" then
		log_message("未找到合适的任务目标", C.r, "【朝廷公差】")
		handle_no_job()
		return
	end

	-- 设置任务变量
	var["killer_name"] = target.name
	var["gf_target_name"] = target.name
	var["killer_id"] = target.id
	var["killer_level"] = target.level_name
	var["gf_job_exp"] = target.exp

	log_message("选择目标：" .. target.name .. "，经验：" .. target.exp, C.g, "【朝廷公差】")

	check_busy(function()
		exec("jie_wenshu")
	end)
end)

add_alias("jie_wenshu", function(params)
	-- 接取文书成功触发器
	add_trigger("guanfu_3",
		"你(?:分开行人来到近前，目光淡淡的扫视了几眼告示|推开围观的行人，走到近前，微微睁开眼睛|走上前去看了看，“唰”地一下把通缉|壮了壮胆，费力拔开行人，来到近前深深吸了口气，竭力镇定)",
		function(params)
			cleanup_jie_wenshu_triggers() -- 只清理接取文书的触发器
			exec("look wenshu")
			log_message("成功接取文书", C.g, "【朝廷公差】")
		end)

	-- 接取文书失败触发器
	add_trigger("guanfu_4",
		"^[ > ]*(?:目前通缉榜上还没出什么官府文书，看来治安还不错|你揭开地上一片瓦片看了看。|此犯的铁捕文书已经全部被揭走了|你想了想，觉得自己的这点功夫，还是算了吧|你揭开地上一块刻.*的方砖看了看)",
		function(params)
			cleanup_all_wenshu_triggers() -- 失败时清理所有触发器
			var["fangqi_job"] = "gf"
			log_message("文书已被揭走或无法接取", C.r, "【朝廷公差】")
			gf_end_job()
		end)

	-- 任务繁忙触发器
	add_trigger("guanfu_5", "^[ > ]*你现在任务缠身，忙不过来呢", function(params)
		cleanup_all_wenshu_triggers() -- 繁忙时清理所有触发器
		set_dazuo("ask_job_guanfu")
		log_message("任务繁忙，前往打坐", C.y, "【朝廷公差】")
		b(function()
			exec("go_dazuo")
		end)
	end)
	--        此人杀人抢劫后，最后一次出现在佛山小路附近。
	-- 地点信息触发器
	add_trigger("guanfu_6", "^\\s+此人.*后，最后一次出现在(\\S+)附近。", function(params)
		local original_location = params[1]
		local corrected_location = fix_location_name(original_location)

		var["job_room"] = corrected_location or original_location

		if corrected_location and corrected_location ~= original_location then
			log_message("地点修正：" .. original_location .. " → " .. corrected_location, C.y, "【朝廷公差】")
		else
			log_message("目标地点：" .. original_location, C.c, "【朝廷公差】")
		end
	end)
	--        此人拦路抢劫后，最后一次出现在沧州东街附近。

	-- 任务类型触发器
	add_trigger("guanfu_7", "^\\s+((?:缉拿归案|就地格杀))的赏金：", function(params)
		cleanup_look_wenshu_triggers() -- 解析完成后清理解析触发器
		var["job_type"] = params[1]

		local location = var["job_room"] or "未知地点"
		local target = var["killer_name"] or "未知目标"
		log_message("任务详情 - 地点：" .. location .. "，类型：" .. params[1] .. "，目标：" .. target, C.W, "【朝廷公差】")

		exec("do_gf")
	end)
	--        缉拿归案的赏金：一锭黄金三十七两白银八十文铜钱。

	check_busy(function()
		exec("jie @killer_id")
	end)
end)

-- ============================================================================
-- 任务执行主函数
-- ============================================================================

-- 检查是否为放弃任务类型
local function should_abandon_job_type(job_type)
	return job_type == "缉拿归案"
end

-- 检查是否为放弃区域
local function should_abandon_zone(zone, fangqi_zone_list)
	return zone and string.find(fangqi_zone_list, zone)
end

-- 处理任务放弃
local function abandon_job(reason, details)
	var["black_list"] = var["black_list"] or {}
	set_black_list(var["killer_name"])
	var["gf_target_name"] = "未知npc"
	var["original_job_room"] = nil
	log_message(reason .. "：" .. (details or ""), C.r, "【朝廷公差】")
	b(function()
		gf_end_job()
	end)
end

-- 执行官府任务
add_alias("do_gf", function(params)
	local job_type = var["job_type"] or "就地格杀"

	-- 保存原始的job_room值，防止被universal_job_search修改
	if not var["original_job_room"] then
		var["original_job_room"] = var["job_room"]
	end

	-- 使用原始的job_room值进行解析
	local job_zone, job_room = break_zone_room(var["original_job_room"])
	local fangqi_zone = get_config_value("gf_fangqi_zone", GUANFU_CONFIG.DEFAULT_FANGQI_ZONE)
	local fangqi_room = get_config_value("gf_fangqi_room", GUANFU_CONFIG.DEFAULT_FANGQI_ROOM)

	-- 记录日志信息
	var["log_zone"] = job_zone
	var["log_room"] = job_room

	log_message("开始处理任务 - 类型：" .. job_type .. "，区域：" .. (job_zone or "未知"), C.c, "【朝廷公差】")

	-- 检查任务类型是否需要放弃
	if should_abandon_job_type(job_type) then
		abandon_job("任务类型为【缉拿归案】", "自动放弃此类任务")
		return
	end

	-- 检查区域是否需要放弃
	if should_abandon_zone(job_zone, fangqi_zone) then
		abandon_job("目标地点【" .. (job_zone or "") .. "】在放弃搜索区域列表中", "自动放弃任务")
		return
	end

	-- ========================================================================
	-- 任务搜索执行部分
	-- ========================================================================

	-- 使用universal_job_search统一搜索框架，支持灵活的搜索次数配置
	local search_attempts = get_config_value("gf_search_attempts", GUANFU_CONFIG.DEFAULT_SEARCH_ATTEMPTS)

	-- 设置官府任务特有的处理逻辑
	local function guanfu_special_handler()
		-- 设置官府任务的所有触发器
		open_guanfu()
		close_trigger("guanfu_26")

		-- NPC信息获取触发器
		add_trigger("guanfu_8", "^\\s+(\\S+)(?:叛徒|弃徒)\\s+" .. var["killer_name"], function(params)
			var["killer_party"] = params[1]
			log_message("发现目标门派：" .. params[1], C.c, "【朝廷公差】")
			exec("kill @killer_id")
			add_trigger("guanfu_25", "^□(\\S+)\\(", function(params)
				del_trigger("guanfu_25")
				var["killer_skill"] = params[1]
			end)
		end)

		-- 战斗胜利触发器（昏迷）
		add_trigger("guanfu_9", "^[ > ]*(?:你双手分使，灵活异常，好象变成了两个人似的！|)" .. var["killer_name"] .. "神志迷糊，脚下一个不稳，倒在地上昏了过去。",
			function(params)
				unset_timer("alarm1")
				close_trigger("guanfu_18")
				log_message("目标昏迷", C.g, "【朝廷公差】")
				if var["job_type"] and var["job_type"] == "缉拿归案" and 1 == 0 then
					exec("set wimpycmd halt")
					fight_end()
					do_log("gf_win")
					bb(function()
						exec("get gold from @killer_id;get silver from @killer_id;get @killer_id;job_win")
					end)
				else
					exec("halt;kill " .. var["killer_id"])
				end
			end)

		-- 战斗胜利触发器（死亡）
		add_trigger("guanfu_10", "^[ > ]*(?:你双手分使，灵活异常，好象变成了两个人似的！|)" .. var["killer_name"] .. "「啪」的一声倒在地上，挣扎着抽动了几下就死了。",
			function(params)
				unset_timer("alarm1")
				unset_timer("no_fight_gf")
				close_trigger("guanfu_18")
				open_trigger("guanfu_23")
				add_alias("job_win", function(params)
					-- 任务成功后先去存文书房间取文书
					g(GUANFU_CONFIG.WENSHU_ROOM, function()
						check_busy(function()
							send("qu tiebu wenshu")
							check_busy(function()
								g(GUANFU_CONFIG.JIAOCHAI_ROOM, function()
									b(function()
										safe_give_to_zhao("corpse")
									end)
								end)
							end)
						end)
					end)
				end)
				fight_end()
				do_log("gf_win")
				bb(function()
					exes("get gold from corpse;get silver from corpse;get corpse;alias action JOB FINISH and GET CORPSE",
						5)
				end)
			end)

		-- 其他战斗相关触发器
		add_trigger("guanfu_11", "^[ > ]*你.*道：承让.*！$", function(params)
			exec("npc_lose")
		end)
		add_trigger("guanfu_12", "^[ > ]*你.*说道：.*(?:佩服|果然高明|在下输了)！$", function(params)
			exec("npc_win")
		end)
		add_trigger("guanfu_13", "^[ > ]*(?:你双手分使，灵活异常，好象变成了两个人似的！|)" ..
			var["killer_name"] .. ".*说道：.*(?:佩服|果然高明|在下输了)！$", function(params)
				exec("npc_lose")
			end)
		add_trigger("guanfu_14", "^[ > ]*" .. var["killer_name"] .. ".*道：承让.*！$", function(params)
			exec("npc_win")
		end)
		add_trigger("guanfu_26", "^[ > ]*这里不准战斗。", function(params)
			set_timer("no_fight_gf", 2, function()
				exec("kill_job_npc")
			end)
		end)

		-- 重写cross_for_job以保持官府任务的特殊逻辑
		add_alias("cross_for_job", function(params)
			check_busy(function()
				exes("follow @killer_id;kill @killer_id;look @killer_id;alias action 奉ET大人命寻找贼寇...", 5)
			end)
		end)

		-- 添加kill_job_npc函数
		add_alias("kill_job_npc", function(params)
			if var["job_type"] and var["job_type"] == "缉拿归案" then
				exec("hit " .. var["killer_id"])
			else
				exec("kill " .. var["killer_id"])
			end
		end)
	end

	-- 先验证目标地点是否可达，只有可达才存储文书
	local room_list, search_list, search_list2, job_zone_check, job_room_check = get_job_rooms(
		var["original_job_room"],                   -- target_area，使用原始值
		5,                                          -- range
		var["killer_name"],                         -- npc_name
		var["killer_party"],                        -- killer_party
		var["killer_skill"],                        -- killer_skill
		table.concat(var["black_list"] or {}, "|"), -- black_list
		var["fear_party"] or "none",                -- fangqi_party
		"none",                                     -- fangqi_skill
		fangqi_room,                                -- fangqi_zone_room
		fangqi_zone,                                -- fangqi_zone
		1                                           -- sort
	)

	if null(room_list) then
		-- 地点解析失败或不可达，直接放弃任务，不存储文书
		var["black_list"] = var["black_list"] or {}
		set_black_list(var["killer_name"]) -- 加入黑名单
		var["gf_target_name"] = "未知npc"
		local target_location = var["original_job_room"] or "未知地点"
		log_message("【朝廷公差】:目标地点【" .. target_location .. "】解析失败或不可达，自动放弃任务！", C.r, "<Lua>", 0)
		-- 清除保存的原始job_room值
		var["original_job_room"] = nil
		b(function()
			gf_end_job() -- 没找到？
		end)
	else
		-- 地点可达，先去存文书房间保存文书，然后调用universal_job_search
		g(GUANFU_CONFIG.WENSHU_ROOM, function()
			check_busy(function()
				send("cun tiebu wenshu")
				var["gf_wenshu_saved"] = true -- 标记已保存文书
				log_message("文书已保存，开始搜索任务", C.g, "【朝廷公差】")
				check_busy(function()
					-- 调用universal_job_search，使用原始的job_room值
					universal_job_search(
						var["original_job_room"], -- target_area，使用原始值
						GUANFU_CONFIG.SEARCH_RANGE, -- search_range
						var["killer_name"], -- npc_name
						search_attempts, -- max_attempts (可配置)
						"follow @killer_id;kill @killer_id;look @killer_id;alias action 奉ET大人命寻找贼寇...", -- custom_action
						"朝廷公差", -- log_prefix
						guanfu_special_handler, -- special_handler
						{
							killer_party = var["killer_party"],
							killer_skill = var["killer_skill"],
							black_list = table.concat(var["black_list"] or {}, "|"),
							fangqi_party = var["fear_party"] or "none",
							fangqi_skill = "none",
							fangqi_zone_room = fangqi_room,
							fangqi_zone = fangqi_zone,
							sort = 1
						}
					)
				end)
			end)
		end)
	end
end)

-- 统一的官府任务结束函数，自动处理文书取回
function gf_end_job()
	-- 清除保存的原始job_room值
	var["original_job_room"] = nil

	-- 检查是否有保存的文书需要取回
	if var["gf_wenshu_saved"] then
		var["gf_wenshu_saved"] = nil -- 清除标记
		log_message("取文书", C.y, "【朝廷公差】")
		g(GUANFU_CONFIG.WENSHU_ROOM, function()
			check_busy(function()
				send("qu tiebu wenshu")
				check_busy(function()
					exec("#2 drop wenshu;changejob")
				end)
			end)
		end)
	else
		-- 如果没有保存过文书，直接放弃任务
		log_message("任务结束", C.y, "【朝廷公差】")
		exec("drop wenshu;changejob")
	end
end

function set_black_list(name) --加入到black_list, 共20个，新的插入到第1个删除第20个
	if name then
		local danger = var["black_list"] or {}
		local in_table = 0
		for k, v in pairs(danger) do
			if string.find(name, v) then
				in_table = 1
			end
		end
		if in_table == 0 and name then
			danger[20] = nil
			table.insert(danger, 1, name)
			var["black_list"] = danger
		end
	end
end

function check_black_list(_name) --检查name是不是在black_list 里面
	if _name then
		local black_list = var["black_list"] or {}
		local in_table = false
		for k, v in pairs(black_list) do
			if string.find(v, _name) then
				in_table = true
			end
		end
		return in_table
	else
		return false
	end
end

add_trigger("guanfu_15", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"奉ET大人命寻找贼寇", function(params)
	del_timer("input")
	table.remove(var["search_list"], 1)
	local do_stop = var["do_stop"] or 0
	if do_stop == 0 then
		local get_npc = check_room_obj(var["killer_name"])
		if get_npc == false then
			keepwalk()
		else
			var["do_stop"] = 1
			send("look " .. var["killer_id"])
			send("alias action 八婺天下说看看情况再动手...")
		end
	end
end)
add_trigger("guanfu_16", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"八婺天下说看看情况再动手", function(params)
	del_timer("input")
	local fear_party = var["fear_party"] or "none"
	local killer_party = var["killer_party"] or "none"
	local killer_skill = var["killer_skill"] or "none"
	--fear_party="武当派|星宿派|古墓派"--预设几个看看？
	echo("\n" .. C.W .. killer_party .. " " .. killer_skill)
	if string.find(fear_party, killer_party) then
		var["black_list"] = var["black_list"] or {}
		var["fangqi_job"] = "gf"
		set_black_list(var["killer_name"]) --加入黑名单
		-- 清除保存的原始job_room值
		var["original_job_room"] = nil
		b(function()
			gf_end_job() --没找到？
		end)
	else
		var["job_finish"] = 0
		var["pfm_id"] = var["killer_id"]
		set_fight("gf")
		exec("kill_job_npc")
		alarm("input", 20, function()
			exec("look;alias action JOB NPC ESCAPED OR NOT")
		end)
	end
end)

add_trigger("guanfu_17", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"八婺天下说累死人不偿命，收工", function(params)
	del_timer("input")
	var["black_list"] = var["black_list"] or {}
	var["fangqi_job"] = "gf"
	set_black_list(var["killer_name"]) --加入黑名单
	-- 清除保存的原始job_room值
	var["original_job_room"] = nil
	gf_end_job() --没找到？	
	close_guanfu()
	log_message("【朝廷公差】:揭榜之后没找到武功【" .. var["killer_level"] .. "】的【" .. var["killer_name"] .. "】。", C.y, "<Lua>", 0)
end)

add_trigger("guanfu_18", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"JOB NPC ESCAPED OR NOT", function(params)
	local get_npc = check_room_obj(var["killer_name"])
	if get_npc == false then
		unset_timer("timer")
		var["do_stop"] = 0
		exec("do_gf")
	else
		alarm("input", 10, function()
			exec("kill @killer_id;look;alias action JOB NPC ESCAPED OR NOT...")
		end)
	end
end)
add_trigger("guanfu_19", "^[ > ]*(\\S+)将(\\S+)的尸体扶了起来背在背上。", function(params)
	var["idle"] = 0
	local killer_name = var["gf_target_name"] or "未知npc"
	if trim(params[1]) == "你" then --本人杀掉的，并且由本人get corpse
		if trim(params[2]) == killer_name then
			unset_timer("alarm1")
			close_trigger("guanfu_18")
			unset_timer("wait")
			exec("job_win")
			log_message("【朝廷公差】:搞定【" .. killer_name .. "】可以回去交差了！", C.g, "<Lua>", 0)
		else
			wait(20, function()
				exec("job_win")
			end)
			exec("drop corpse;get corpse 2")
		end
	else
		if trim(params[2]) == killer_name then --尸体被别人get ，那么直接任务失败
			close_guanfu()
			close_trigger("guanfu_19")
			set_black_list(trim(params[2])) --被抢走的尸体名字加入黑名单			
			log_message("【朝廷公差】:已经杀死【" .. killer_name .. "】，但是" .. killer_name .. "的尸体被【" .. trim(params[1]) .. "】抢走了！",
				C.r, "<Lua>", 0)
			b(function()
				gf_end_job()
			end)
		end
	end
end)

add_trigger("guanfu_20", "^[ > ]*你给赵城之一具(.*)的(尸体|男尸|女尸)", function(params) -- 你给赵城之一具(\\S+)的尸体
	var["idle"] = 0
	var["gf_corpse_name"] = params[1] or ""
	close_guanfu()
	b(function()
		gf_end_job()
	end)
end)


add_trigger("guanfu_reward_1", "^[ > ]*你得到了赏金.*，(\\S+)点潜能和(\\S+)点经验！", function(params)
	var["log_pot"] = params[1]
	var["log_exp"] = params[2]
	do_log("job_reward")	
end)

add_trigger("guanfu_23", "^[ > ]*赵城之说道：「没看见本官正忙着吗？一边等着！」", function(params)
	log_message("【朝廷公差】:尸体没送出去，赵城之忙呢！！", C.r, "<Lua>", 0)
	wait(2, function()
		safe_give_to_zhao("corpse")
		log_message("【朝廷公差】:尸体再送一遍！", C.r, "<Lua>", 0)
	end)
end)
add_trigger("guanfu_24", "^[ > ]*这个尸体不属于你", function(params)
	exec("drop corpse;get corpse 2")
end)

--alias action 官府恶贼还在不...
function open_guanfu()
	open_trigger("guanfu_1")
	open_triggers("guanfu", 15, 27) -- 恢复到26，不包含临时触发器
	setup_hp_monitor("guanfu_27")
end

function close_guanfu()
	close_trigger("guanfu_1")
	delete_triggers("guanfu", 2, 14)
	close_triggers("guanfu", 15, 27) -- 恢复到26，不包含临时触发器
	remove_hp_monitor("guanfu_27")
end

close_guanfu()